#include "my_scheduler.h"

typedef struct{
	void(*task_fun)(void);
	uint32_t task_time;
	uint32_t last_time;
}task;

// 简化任务列表，只保留巡线必需的任务
task all_task[]={
	{led_task,50,0},        // LED状态指示，降低频率
	{Gray_Task,5,0},        // 灰度传感器读取
	{Encoder_Task,5,0},     // 编码器读取
	{PID_Task,5,0},         // PID控制
	{key_task,20,0},        // 按键检测，降低频率
};

uint8_t task_num;

void all_task_init(void)
{
	task_num = sizeof(all_task)/sizeof(task);

	// 只初始化巡线必需的模块
	uart_init();            // 串口通信（可选，用于调试）
	led_init();             // LED状态指示
	PID_Init();             // PID控制器
	Encoder_Init();         // 编码器
	Motor_Init();           // 电机驱动
	Gray_Init();            // 灰度传感器
	timer_init();           // 定时器
}

void all_task_run(void)
{
	for(uint8_t i=0;i<task_num;i++)
	{
		uint32_t now_time = HAL_GetTick();
		if(now_time >= all_task[i].last_time + all_task[i].task_time)
		{
			all_task[i].last_time = now_time;
			all_task[i].task_fun();
		}
	}	
}
