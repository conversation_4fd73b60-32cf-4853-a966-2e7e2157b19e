#ifndef __MOTOR_APP_H_
#define __MOTOR_APP_H_

#include "mydefine.h"
#include "motor_driver.h"

void Motor_Init(void);
void motor_set_l(float speed);
void motor_set_r(float speed);
void motor_break(void);
void motor_set_decay_mode(MotorDecayMode_t mode);

// 最底层电机控制函数
void motor_direct_pwm_control(float left_duty, float right_duty);
void motor_run_20_percent(void);
void motor_stop_direct(void);
void motor_enable_line_following(void);
void motor_line_following_diagnosis(void);

#endif
