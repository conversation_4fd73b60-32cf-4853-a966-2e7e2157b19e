#include "motor_app.h"

Motor_t right_motor;
Motor_t left_motor; 

void Motor_Init(void)
{
  // 左电机：AIN1=TIM1_CH1, AIN2=TIM1_CH2, 正装
  Motor_Create(&left_motor, &htim1,
               TIM_CHANNEL_1, TIM_CHANNEL_2,           // AIN1, AIN2 PWM通道
               GPIOE, GPIO_PIN_9, GPIO_AF1_TIM1,       // AIN1 GPIO + AF
               GPIOE, GPIO_PIN_11, GPIO_AF1_TIM1,      // AIN2 GPIO + AF
               1);                                     // 正装

  // 右电机：AIN1=TIM1_CH3, AIN2=TIM1_CH4, 反装
  Motor_Create(&right_motor, &htim1,
               TIM_CHANNEL_3, TIM_CHANNEL_4,           // AIN1, AIN2 PWM通道
               GPIOE, GPIO_PIN_13, GPIO_AF1_TIM1,      // AIN1 GPIO + AF
               GPIOE, GPIO_PIN_14, GPIO_AF1_TIM1,      // AIN2 GPIO + AF
               0);                                     // 反装
}

//speed -100.0 ~ +100.0, 支持一位小数精度
// 全局速度缩放系数 - 适合巡线的速度
float global_speed_scale = 0.6f; // 60%速度，适合巡线控制

void motor_set_l(float speed)
{
	// 应用速度缩放系数
	float scaled_speed = speed * global_speed_scale;
	Motor_SetSpeed(&left_motor, scaled_speed);
}

void motor_set_r(float speed)
{
	// 应用速度缩放系数
	float scaled_speed = speed * global_speed_scale;
	Motor_SetSpeed(&right_motor, scaled_speed);
}

void motor_break(void)
{
	Motor_Stop(&right_motor);
	Motor_Stop(&left_motor);
}

// 设置电机衰减模式 (可选功能)
// MOTOR_DECAY_SLOW: 慢衰减，适合低速精确控制
// MOTOR_DECAY_FAST: 快衰减，适合高速运行和快速响应
void motor_set_decay_mode(MotorDecayMode_t mode)
{
	Motor_SetDecayMode(&left_motor, mode);
	Motor_SetDecayMode(&right_motor, mode);
}

/**
 * @brief 最底层电机控制 - 直接设置PWM占空比
 * @param left_duty: 左电机占空比 (0-100)
 * @param right_duty: 右电机占空比 (0-100)
 * @note 完全绕过所有上层控制逻辑，直接控制硬件PWM
 */
void motor_direct_pwm_control(float left_duty, float right_duty)
{
    // 计算PWM比较值 (PWM周期是999)
    uint32_t left_pwm = (uint32_t)(left_duty * 999.0f / 100.0f);
    uint32_t right_pwm = (uint32_t)(right_duty * 999.0f / 100.0f);

    // 限制PWM值范围
    if (left_pwm > 999) left_pwm = 999;
    if (right_pwm > 999) right_pwm = 999;

    // 直接设置左电机PWM (慢衰减模式：AIN1=PWM, AIN2=0)
    HAL_GPIO_WritePin(GPIOE, GPIO_PIN_11, GPIO_PIN_RESET);  // AIN2=0
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, left_pwm); // AIN1=PWM

    // 直接设置右电机PWM (慢衰减模式：AIN1=PWM, AIN2=0)
    HAL_GPIO_WritePin(GPIOE, GPIO_PIN_14, GPIO_PIN_RESET);  // AIN2=0
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_3, right_pwm); // AIN1=PWM
}

/**
 * @brief 20%占空比电机驱动 - 最简单的调用方式
 */
void motor_run_20_percent(void)
{
    
}

/**
 * @brief 停止电机 - 最底层停止
 */
void motor_stop_direct(void)
{
    motor_direct_pwm_control(0.0f, 0.0f); // 左右电机都0%占空比
}

/**
 * @brief 切换到循迹模式
 */
void motor_enable_line_following(void)
{
    extern unsigned char pid_control_mode;
    extern bool pid_running;
    extern UART_HandleTypeDef huart1;

    pid_control_mode = 1;  // 切换到循迹模式
    pid_running = true;    // 启动PID控制

    my_printf(&huart1, "Line Following Mode ENABLED!\r\n");
    my_printf(&huart1, "pid_control_mode=%d, pid_running=%d\r\n", pid_control_mode, pid_running);
}

/**
 * @brief 循迹诊断函数 - 检查所有循迹相关状态
 */
void motor_line_following_diagnosis(void)
{
    extern unsigned char pid_control_mode;
    extern bool pid_running;
    extern float g_line_position_error;
    extern UART_HandleTypeDef huart1;
    extern Encoder left_encoder, right_encoder;

    my_printf(&huart1, "=== Line Following Diagnosis ===\r\n");
    my_printf(&huart1, "PID Running: %d\r\n", pid_running);
    my_printf(&huart1, "Control Mode: %d (0=Angle, 1=Line)\r\n", pid_control_mode);
    my_printf(&huart1, "Line Error: %.2f\r\n", g_line_position_error);
    my_printf(&huart1, "Left Speed: %.2f cm/s\r\n", left_encoder.speed_cm_s);
    my_printf(&huart1, "Right Speed: %.2f cm/s\r\n", right_encoder.speed_cm_s);
    my_printf(&huart1, "Speed Scale: %.2f\r\n", global_speed_scale);
    my_printf(&huart1, "===============================\r\n");
}
