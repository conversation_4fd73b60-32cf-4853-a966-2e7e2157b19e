#include "gray_app.h"
#include "software_iic.h" // 直接使用软件I2C

extern UART_HandleTypeDef huart1;

unsigned char Digtal; 

float gray_weights[8] = {-4.0f, -3.0f, -2.0f, -1.0f, 1.0f, 2.0f, 3.0f, 4.0f}; 

float g_line_position_error; 

void Gray_Init(void)
{
    // 测试软件I2C连接
    if(Ping() == 0) {
        my_printf(&huart1, "Gray Sensor Connected Successfully!\r\n");
    } else {
        my_printf(&huart1, "Gray Sensor Connection Failed!\r\n");
    }
}

void Gray_Task(void)
{
    uint8_t temp = 0;
    temp = IIC_Get_Digtal();
    if(temp == 0xAA)
    {
        return; // 读取失败，直接返回
    }
    Digtal = ~temp; // 取反，黑线为1，白线为0

    // 计算线位置误差
    float weighted_sum = 0;
    uint8_t black_line_count = 0;

    for(uint8_t i = 0; i < 8; i++)
    {
        if((Digtal>>i) & 0x01)
        {
            weighted_sum += gray_weights[i];
            black_line_count++;
        }
    }

    // 更新线位置误差
    if(black_line_count > 0)
        g_line_position_error = weighted_sum / (float)black_line_count;
    else
        g_line_position_error = 0; // 没有检测到线，误差为0
}
