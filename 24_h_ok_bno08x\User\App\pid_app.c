#include "pid_app.h"

#define V_L_MAX 100
#define V_R_MAX 100

extern UART_HandleTypeDef huart1;

extern Encoder left_encoder;
extern Encoder right_encoder;

int basic_speed = 15; // 基础速度 - 提高到15cm/s，适合巡线

/* PID ������ʵ�� */
PID_T pid_speed_left;  // �����ٶȻ�
PID_T pid_speed_right; // �����ٶȻ�

PID_T pid_line;        // ѭ����

PID_T pid_angle;       // �ǶȻ�

/* PID �������� */
//PidParams_t pid_params_left = {
//    .kp = 6.8f,     // ��ǿ�ı������ƣ��ӿ������ٶ�
//    .ki = 0.175f,     // ��߻����ٶȣ��ӿ쿿��Ŀ��
//    .kd = 0.5f,     // �ʵ�΢������ĩ�ڹ���
//    .out_min = -V_L_MAX,
//    .out_max = V_L_MAX,
//};

//PidParams_t pid_params_right = {
//    .kp = 7.0f,     // ��ǿ�ı������ƣ��ӿ������ٶ�
//    .ki = 0.2f,     // ��߻����ٶȣ��ӿ쿿��Ŀ��
//    .kd = 0.4f,     // �ʵ�΢������ĩ�ڹ���
//    .out_min = -V_R_MAX,
//    .out_max = V_R_MAX,
//};
PidParams_t pid_params_left = {
    .kp = 3.0f,     // ����һ�������Ӧ�����������ٶ�
    .ki = 0.1f,     // ������֣����ٸ�Ŀ������̬���
    .kd = 0.3f,     // ��ǿ΢�֣������и��ٶ��µ�С����
		.out_min = -V_L_MAX,
    .out_max = V_L_MAX,

};
PidParams_t pid_params_right = {
    .kp = 3.0f,     // ��΢�������������͸��ٶ�����С����
    .ki = 0.1f,     // �����ֱ������������һ����
    .kd = 0.3f,     // �Լ�ǿ΢�֣�������������΢����
	  .out_min = -V_R_MAX,
    .out_max = V_R_MAX,
};


PidParams_t pid_params_line = {
    .kp = 3.0f,     // 降低Kp，适应低速循迹
    .ki = 0.0f,
    .kd = 0.1f,     // 增加一点微分，提高稳定性
    .out_min = -20.0f,  // 降低输出范围，适应低速
    .out_max = 20.0f,
};


PidParams_t pid_params_angle = {
    .kp = 1.0f,        
    .ki = 0.000f,      
    .kd = 0.00f,      
    .out_min = -999.0f,
    .out_max = 999.0f,
};

void PID_Init(void)
{
  pid_init(&pid_speed_left,
           pid_params_left.kp, pid_params_left.ki, pid_params_left.kd,
           0.0f, pid_params_left.out_max);
  
  pid_init(&pid_speed_right,
           pid_params_right.kp, pid_params_right.ki, pid_params_right.kd,
           0.0f, pid_params_right.out_max);
  

  pid_init(&pid_line,
           pid_params_line.kp, pid_params_line.ki, pid_params_line.kd,
           0.0f, pid_params_line.out_max);
//  
  pid_init(&pid_angle,
           pid_params_angle.kp, pid_params_angle.ki, pid_params_angle.kd,
           0.0f, pid_params_angle.out_max);
  
  
  pid_set_target(&pid_speed_left, basic_speed);
  pid_set_target(&pid_speed_right, basic_speed);
  pid_set_target(&pid_line, 0);
  pid_set_target(&pid_angle, 0);
}

bool pid_running = false; // PID运行使能控制

unsigned char pid_control_mode = 1; // 默认设置为循线控制模式

void Line_PID_control(void) // 循线控制
{
  int line_pid_output = 0;

  // 使用位置式PID计算循线控制输出
  line_pid_output = pid_calculate_positional(&pid_line, g_line_position_error);

  // 输出限幅
  line_pid_output = pid_constrain(line_pid_output, pid_params_line.out_min, pid_params_line.out_max);

  // 设置左右电机目标速度（差速转向）
  pid_set_target(&pid_speed_left, basic_speed - line_pid_output);
  pid_set_target(&pid_speed_right, basic_speed + line_pid_output);
}

void Angle_PID_control(void) // �ǶȻ�����
{
  int angle_pid_output = 0;
  
	float Yaw = get_yaw();
	
  // ʹ��λ��ʽ PID �������ýǶȻ��������
  angle_pid_output = pid_calculate_positional(&pid_angle, Yaw);
  
  // ����޷�
  angle_pid_output = pid_constrain(angle_pid_output, pid_params_angle.out_min, pid_params_angle.out_max);
  
  // ����ֵ�������ٶȻ���Ŀ������
  pid_set_target(&pid_speed_left, basic_speed - angle_pid_output);
  pid_set_target(&pid_speed_right, basic_speed + angle_pid_output);
}

uint8_t stop_flat = 0;

void PID_Task(void)
{
    if(pid_running == false) return;

    float output_left = 0, output_right = 0;

    // 简化控制逻辑，只使用循线控制
    Line_PID_control();
	
//		static uint16_t num = 0;
//		static uint8_t temp = 0;
//		if(++temp >= 50)
//		{
//			temp = 0;
//			num += 20;
//		}
//		
//		pid_set_target(&pid_speed_left, num);
//    pid_set_target(&pid_speed_right, num);

    // 检查停止标志
    if(stop_flat == 1)
    {
        pid_set_target(&pid_speed_left, 0);
        pid_set_target(&pid_speed_right, 0);
    }

    // 使用位置式PID计算电机速度控制输出
    output_left = pid_calculate_positional(&pid_speed_left, left_encoder.speed_cm_s);
    output_right = pid_calculate_positional(&pid_speed_right, right_encoder.speed_cm_s);

    // 输出限幅
    output_left = pid_constrain(output_left, pid_params_left.out_min, pid_params_left.out_max);
    output_right = pid_constrain(output_right, pid_params_right.out_min, pid_params_right.out_max);

    // 设置电机速度
    float duty_l = output_left  / V_L_MAX * 100.0f;
    float duty_r = output_right / V_R_MAX * 100.0f;

    motor_set_l(duty_l);
    motor_set_r(duty_r);
		
//		my_printf(&huart1, "%f,%f\r\n", pid_line.target, g_line_position_error);
		my_printf(&huart1, "Target:%.1f, L:%.1f, R:%.1f\r\n", pid_speed_left.target, left_encoder.speed_cm_s, right_encoder.speed_cm_s);
//		my_printf(&huart1, "%f,%f\r\n", pid_speed_right.target, right_encoder.speed_cm_s);
}


