# 小车巡线功能说明

## 修改内容

### 1. 简化任务调度器 (my_scheduler.c)
- 移除了不必要的任务：bno080_task, oled_task
- 只保留巡线必需的任务：
  - Gray_Task: 灰度传感器读取 (5ms周期)
  - Encoder_Task: 编码器读取 (5ms周期)  
  - PID_Task: PID控制 (5ms周期)
  - led_task: LED状态指示 (50ms周期)
  - key_task: 按键检测 (20ms周期)

### 2. 优化灰度传感器 (gray_app.c)
- 移除了调试输出，提高运行效率
- 简化了传感器数据处理逻辑
- 增加了错误处理（当没有检测到线时，误差设为0）

### 3. 简化PID控制 (pid_app.c)
- 默认设置为循线控制模式 (pid_control_mode = 1)
- 移除了角度控制相关的复杂逻辑
- 提高了基础速度到15cm/s
- 移除了调试输出，提高性能

### 4. 简化按键控制 (key_app.c)
- Key1: 启动巡线（绿色LED）
- Key2: 停止巡线（红色LED）
- Key3: 重置系统（蓝色LED）
- Key4: 预留功能
- User Key: 预留功能

### 5. 调整电机参数 (motor_app.c)
- 将速度缩放系数从30%提高到60%
- 更适合巡线控制的响应速度

## 使用方法

### 启动巡线
1. 将小车放在黑线上
2. 按下Key1启动巡线
3. LED显示绿色表示正在巡线

### 停止巡线
1. 按下Key2停止巡线
2. LED显示红色表示已停止

### 重置系统
1. 按下Key3重置所有PID控制器
2. LED显示蓝色表示已重置

## 参数调整

如果巡线效果不理想，可以调整以下参数：

### 速度参数 (pid_app.c)
```c
int basic_speed = 15; // 基础速度，可调整为10-25
```

### 电机速度缩放 (motor_app.c)
```c
float global_speed_scale = 0.6f; // 可调整为0.4-0.8
```

### PID参数 (pid_app.c)
循线PID参数在 `pid_params_line` 结构体中定义，可根据实际效果调整。

## 故障排除

1. **小车不动**：检查pid_running是否为true，按Key1启动
2. **巡线不稳定**：降低basic_speed或调整PID参数
3. **转向过度**：降低循线PID的Kp参数
4. **转向不足**：提高循线PID的Kp参数

## 注意事项

1. 确保灰度传感器正常工作
2. 确保编码器正常反馈速度
3. 黑线宽度应适中（建议15-20mm）
4. 环境光线要稳定
